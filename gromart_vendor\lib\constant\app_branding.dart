/// Centralized App Branding Configuration
/// 
/// This file contains all app branding information that can be changed from one place.
/// When you want to rebrand the application, simply update the values in this file
/// and the changes will reflect throughout the entire application.
/// 
/// IMPORTANT: After changing these values, you may need to:
/// 1. Run 'flutter clean' and 'flutter pub get'
/// 2. Update app icons and splash screen images if needed
/// 3. Update Firebase project configuration if app names change significantly

class AppBranding {
  // ============================================================================
  // MAIN BRAND CONFIGURATION - CHANGE THESE VALUES TO REBRAND THE APP
  // ============================================================================
  
  /// Base brand name (e.g., "MedsyPlus", "GroMart", "FoodieApp")
  static const String baseBrandName = "MedsyPlus";
  
  /// Customer app specific name
  static const String customerAppName = "$baseBrandName Customer";
  
  /// Vendor/Store app specific name  
  static const String vendorAppName = "$baseBrandName Store";
  
  /// Driver/Delivery app specific name
  static const String driverAppName = "$baseBrandName Delivery";
  
  // ============================================================================
  // WELCOME MESSAGES AND DESCRIPTIONS
  // ============================================================================
  
  /// Welcome message for customer app
  static const String customerWelcomeMessage = "Welcome to $baseBrandName";
  
  /// Welcome message for vendor app
  static const String vendorWelcomeMessage = "Welcome to $baseBrandName Store";
  
  /// Welcome message for driver app
  static const String driverWelcomeMessage = "Welcome to $baseBrandName Delivery";
  
  /// Customer app description
  static const String customerAppDescription = 
      "Shop from multiple vendors and enjoy a seamless Medicine shopping experience right from your phone.";
  
  /// Vendor app description
  static const String vendorAppDescription = 
      "Manage your store, track orders, and grow your business with $baseBrandName Store.";
  
  /// Driver app description
  static const String driverAppDescription = 
      "Deliver orders efficiently and earn money with $baseBrandName Delivery.";
  
  // ============================================================================
  // LOGIN AND AUTHENTICATION MESSAGES
  // ============================================================================
  
  /// Login page description for customer app
  static const String customerLoginDescription = 
      "Sign in to access your $baseBrandName account and enjoy seamless shopping.";
  
  /// Login page description for vendor app
  static const String vendorLoginDescription = 
      "Sign in to access your $baseBrandName Store account and manage your business.";
  
  /// Login page description for driver app
  static const String driverLoginDescription = 
      "Sign in to access your $baseBrandName account and manage your deliveries seamlessly.";
  
  // ============================================================================
  // PLATFORM SPECIFIC NAMES (for app stores, manifests, etc.)
  // ============================================================================
  
  /// Short name for customer app (used in manifests, app stores)
  static const String customerAppShortName = "customer";
  
  /// Short name for vendor app (used in manifests, app stores)
  static const String vendorAppShortName = "store";
  
  /// Short name for driver app (used in manifests, app stores)
  static const String driverAppShortName = "driver";
  
  /// Display name for customer app (shown on device home screen)
  static const String customerDisplayName = "Customer";
  
  /// Display name for vendor app (shown on device home screen)
  static const String vendorDisplayName = "Store";
  
  /// Display name for driver app (shown on device home screen)
  static const String driverDisplayName = "Driver";
  
  // ============================================================================
  // HELPER METHODS
  // ============================================================================
  
  /// Get the appropriate app name based on app type
  static String getAppName(AppType appType) {
    switch (appType) {
      case AppType.customer:
        return customerAppName;
      case AppType.vendor:
        return vendorAppName;
      case AppType.driver:
        return driverAppName;
    }
  }
  
  /// Get the appropriate welcome message based on app type
  static String getWelcomeMessage(AppType appType) {
    switch (appType) {
      case AppType.customer:
        return customerWelcomeMessage;
      case AppType.vendor:
        return vendorWelcomeMessage;
      case AppType.driver:
        return driverWelcomeMessage;
    }
  }
  
  /// Get the appropriate app description based on app type
  static String getAppDescription(AppType appType) {
    switch (appType) {
      case AppType.customer:
        return customerAppDescription;
      case AppType.vendor:
        return vendorAppDescription;
      case AppType.driver:
        return driverAppDescription;
    }
  }
  
  /// Get the appropriate login description based on app type
  static String getLoginDescription(AppType appType) {
    switch (appType) {
      case AppType.customer:
        return customerLoginDescription;
      case AppType.vendor:
        return vendorLoginDescription;
      case AppType.driver:
        return driverLoginDescription;
    }
  }
}

/// Enum to identify different app types
enum AppType {
  customer,
  vendor,
  driver,
}
