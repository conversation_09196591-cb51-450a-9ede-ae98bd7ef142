#!/usr/bin/env dart

/// <PERSON><PERSON><PERSON> to update platform-specific app names
/// Run this script after updating the baseBrandName in app_branding.dart files
/// 
/// Usage: dart update_platform_names.dart

import 'dart:io';

// Import the branding configuration
// Note: You'll need to update this path based on your project structure
const String baseBrandName = "MedsyPlus"; // Update this to match your app_branding.dart

const String customerAppName = "$baseBrandName Customer";
const String vendorAppName = "$baseBrandName Store";
const String driverAppName = "$baseBrandName Delivery";

void main() async {
  print('🚀 Updating platform-specific app names...');
  print('Base brand name: $baseBrandName');
  print('Customer app: $customerAppName');
  print('Vendor app: $vendorAppName');
  print('Driver app: $driverAppName');
  print('');

  try {
    // Update Android manifest files
    await updateAndroidManifests();
    
    // Update iOS Info.plist files
    await updateiOSInfoPlist();
    
    // Update web manifest files
    await updateWebManifests();
    
    print('✅ All platform-specific files updated successfully!');
    print('');
    print('📝 Next steps:');
    print('1. Run "flutter clean" and "flutter pub get" for each app');
    print('2. Test all three apps to ensure branding appears correctly');
    print('3. Update app icons and splash screens if needed');
    
  } catch (e) {
    print('❌ Error updating files: $e');
    exit(1);
  }
}

Future<void> updateAndroidManifests() async {
  print('📱 Updating Android manifest files...');
  
  // Customer app
  await updateAndroidManifest(
    'gromart_customer/android/app/src/main/AndroidManifest.xml',
    customerAppName
  );
  
  // Vendor app
  await updateAndroidManifest(
    'gromart_vendor/android/app/src/main/AndroidManifest.xml',
    vendorAppName
  );
  
  // Driver app
  await updateAndroidManifest(
    'gromart_driver/android/app/src/main/AndroidManifest.xml',
    driverAppName
  );
}

Future<void> updateAndroidManifest(String filePath, String appName) async {
  final file = File(filePath);
  if (!await file.exists()) {
    print('⚠️  Warning: $filePath not found');
    return;
  }
  
  String content = await file.readAsString();
  
  // Update android:label
  content = content.replaceAllMapped(
    RegExp(r'android:label="[^"]*"'),
    (match) => 'android:label="$appName"'
  );
  
  await file.writeAsString(content);
  print('✓ Updated $filePath');
}

Future<void> updateiOSInfoPlist() async {
  print('🍎 Updating iOS Info.plist files...');
  
  // Customer app
  await updateiOSInfoPlistFile(
    'gromart_customer/ios/Runner/Info.plist',
    customerAppName
  );
  
  // Vendor app
  await updateiOSInfoPlistFile(
    'gromart_vendor/ios/Runner/Info.plist',
    vendorAppName
  );
  
  // Driver app
  await updateiOSInfoPlistFile(
    'gromart_driver/ios/Runner/Info.plist',
    driverAppName
  );
}

Future<void> updateiOSInfoPlistFile(String filePath, String appName) async {
  final file = File(filePath);
  if (!await file.exists()) {
    print('⚠️  Warning: $filePath not found');
    return;
  }
  
  String content = await file.readAsString();
  
  // Update CFBundleDisplayName
  content = content.replaceAllMapped(
    RegExp(r'<key>CFBundleDisplayName</key>\s*<string>[^<]*</string>'),
    (match) => '<key>CFBundleDisplayName</key>\n\t\t<string>$appName</string>'
  );
  
  await file.writeAsString(content);
  print('✓ Updated $filePath');
}

Future<void> updateWebManifests() async {
  print('🌐 Updating web manifest files...');
  
  // Customer app
  await updateWebManifest(
    'gromart_customer/web/manifest.json',
    customerAppName
  );
  
  // Vendor app
  await updateWebManifest(
    'gromart_vendor/web/manifest.json',
    vendorAppName
  );
  
  // Driver app
  await updateWebManifest(
    'gromart_driver/web/manifest.json',
    driverAppName
  );
}

Future<void> updateWebManifest(String filePath, String appName) async {
  final file = File(filePath);
  if (!await file.exists()) {
    print('⚠️  Warning: $filePath not found');
    return;
  }
  
  String content = await file.readAsString();
  
  // Update name and short_name
  content = content.replaceAllMapped(
    RegExp(r'"name":\s*"[^"]*"'),
    (match) => '"name": "$appName"'
  );
  
  content = content.replaceAllMapped(
    RegExp(r'"short_name":\s*"[^"]*"'),
    (match) => '"short_name": "$appName"'
  );
  
  await file.writeAsString(content);
  print('✓ Updated $filePath');
}
