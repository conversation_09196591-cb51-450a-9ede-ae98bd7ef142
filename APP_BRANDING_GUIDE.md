# App Branding Configuration Guide

## Overview
This guide explains how to change all application names and branding from one central location in your Flutter multi-app project (Customer, Vendor/Store, Driver/Delivery apps).

## 🎯 Quick Start - How to Change App Names

### Step 1: Update the Central Branding File
To change all app names throughout your applications, edit the `baseBrandName` in these files:

- `gromart_customer/lib/constant/app_branding.dart`
- `gromart_vendor/lib/constant/app_branding.dart`  
- `gromart_driver/lib/constant/app_branding.dart`

**Example: Change from "MedsyPlus" to "FoodieApp"**
```dart
// Change this line in all three app_branding.dart files:
static const String baseBrandName = "FoodieApp"; // Changed from "MedsyPlus"
```

### Step 2: Clean and Rebuild
After making changes, run these commands for each app:
```bash
cd gromart_customer
flutter clean
flutter pub get

cd ../gromart_vendor  
flutter clean
flutter pub get

cd ../gromart_driver
flutter clean
flutter pub get
```

## 📱 What Gets Updated Automatically

When you change the `baseBrandName`, these will automatically update:

### Customer App:
- App title: "FoodieApp Customer"
- Welcome message: "Welcome to FoodieApp"
- App description: Uses FoodieApp in the description

### Vendor App:
- App title: "FoodieApp Store" 
- Welcome message: "Welcome to FoodieApp Store"

### Driver App:
- App title: "FoodieApp Delivery"
- Welcome message: "Welcome to FoodieApp Delivery"

## 🔧 Advanced Customization

### Individual App Names
If you want different names for each app, modify these constants in `app_branding.dart`:

```dart
/// Customer app specific name
static const String customerAppName = "MyMedicine Customer";

/// Vendor/Store app specific name  
static const String vendorAppName = "MyMedicine Store";

/// Driver/Delivery app specific name
static const String driverAppName = "MyMedicine Delivery";
```

### Welcome Messages
Customize welcome messages:
```dart
/// Welcome message for customer app
static const String customerWelcomeMessage = "Welcome to MyMedicine";

/// Welcome message for vendor app
static const String vendorWelcomeMessage = "Welcome to MyMedicine Store";

/// Welcome message for driver app
static const String driverWelcomeMessage = "Welcome to MyMedicine Delivery";
```

### App Descriptions
Update app descriptions:
```dart
/// Customer app description
static const String customerAppDescription = 
    "Shop from multiple vendors and enjoy a seamless Medicine shopping experience right from your phone.";

/// Vendor app description
static const String vendorAppDescription = 
    "Manage your store, track orders, and grow your business with MyMedicine Store.";

/// Driver app description
static const String driverAppDescription = 
    "Deliver orders efficiently and earn money with MyMedicine Delivery.";
```

## 📋 Files That Use Centralized Branding

### Automatically Updated Files:
- `gromart_customer/lib/main.dart` - App title
- `gromart_customer/lib/app/splash_screen.dart` - Welcome message and description
- `gromart_vendor/lib/main.dart` - App title  
- `gromart_vendor/lib/app/splash_screen.dart` - Welcome message
- `gromart_driver/lib/main.dart` - App title
- `gromart_driver/lib/app/splash_screen.dart` - Welcome message
- `gromart_driver/lib/lang/app_en.dart` - Login descriptions and other text

### Files You May Need to Update Manually:
- Android manifest files (`android/app/src/main/AndroidManifest.xml`)
- iOS Info.plist files (`ios/Runner/Info.plist`)
- Web manifest files (`web/manifest.json`)
- Firebase configuration files
- App icons and splash screen images

## 🚀 Platform-Specific Updates

### Android App Names (Manual Update Required)
Update the `android:label` in AndroidManifest.xml files:

**Customer App:** `gromart_customer/android/app/src/main/AndroidManifest.xml`
```xml
<application android:label="MyMedicine Customer" ...>
```

**Vendor App:** `gromart_vendor/android/app/src/main/AndroidManifest.xml`
```xml
<application android:label="MyMedicine Store" ...>
```

**Driver App:** `gromart_driver/android/app/src/main/AndroidManifest.xml`
```xml
<application android:label="MyMedicine Delivery" ...>
```

### iOS App Names (Manual Update Required)
Update the `CFBundleDisplayName` in Info.plist files:

**Customer App:** `gromart_customer/ios/Runner/Info.plist`
```xml
<key>CFBundleDisplayName</key>
<string>MyMedicine Customer</string>
```

### Web App Names (Manual Update Required)
Update the `name` and `short_name` in manifest.json files:

**Customer App:** `gromart_customer/web/manifest.json`
```json
{
    "name": "MyMedicine Customer",
    "short_name": "MyMedicine Customer",
    ...
}
```

## ⚠️ Important Notes

1. **Consistency**: Make sure to update the `baseBrandName` in all three `app_branding.dart` files to maintain consistency.

2. **Firebase**: If you change app names significantly, you may need to update Firebase project configuration.

3. **App Store**: When publishing to app stores, make sure the names in platform-specific files match your app store listings.

4. **Testing**: After making changes, test all three apps to ensure branding appears correctly.

5. **Translation Files**: If you have multiple language files, you may need to update branding text in other language files as well.

## 🔄 Future Updates

To add new branding elements:
1. Add new constants to the `AppBranding` class
2. Update the relevant UI files to use the new constants
3. Follow the same pattern for centralized management

This system ensures that future branding changes can be made quickly and consistently across all applications.
