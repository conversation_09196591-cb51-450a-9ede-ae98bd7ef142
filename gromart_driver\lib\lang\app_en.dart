import '../constant/app_branding.dart';

const Map<String, String> enUS = {
  'Log In to Your Account': 'Log In to Your Account',
  'Sign in to access your GroMart account and manage your deliveries seamlessly.':
      AppBranding.driverLoginDescription,
  'Sign in to access your Foodie account and manage your deliveries seamlessly.':
      AppBranding.driverLoginDescription,
  "Didn’t Have an account?": "Didn’t Have an account?",
  'Sign up': 'Sign up',
  'No Conversion found': 'No Conversion found',
  'Type message here....': 'Type message here....',
  'Send Media': 'Send Media',
  'Choose image from gallery': 'Choose image from gallery',
  'Choose video from gallery': 'Choose video from gallery',
  'Take a picture': 'Take a picture',
  'Record video': 'Record video',
  'Cancel': 'Cancel',
  'Please wait': 'Please wait',
  'Welcome Back 👋': 'Welcome Back 👋',
  'Document verification is pending. Please proceed to set up your document verification.':
      'Document verification is pending. Please proceed to set up your document verification.',
  'Available Status': 'Available Status',
  'About App': 'About App',
  'Home': 'Home',
  'Wallet': 'Wallet',
  "Orders": "Orders",
  "Withdrawal Method": "Withdrawal Method",
  "Document Verification": "Document Verification",
  "Inbox": "Inbox",
  "App Preferences": "App Preferences",
  "Change Language": "Change Language",
  "Dark Mode": "Dark Mode",
  "Social": "Social",
  "Share app": "Share app",
  'Check out Foodie, your ultimate food delivery application!':
      'Check out ${AppBranding.baseBrandName}, your ultimate delivery application!',
  'Google Play:': 'Google Play:',
  'App Store:': 'App Store:',
  'Look what I made!': 'Look what I made!',
  'Check out GroMart, your ultimate food delivery application! \n\nGoogle Play:':
      'Check out ${AppBranding.baseBrandName}, your ultimate delivery application! \n\nGoogle Play:',
  'Rate the app': 'Rate the app',
  "Legal": "Legal",
  "Terms and Conditions": "Terms and Conditions",
  'Privacy Policy': 'Privacy Policy',
  "Log out": "Log out",
  "Are you sure you want to log out? You will need to enter your credentials to log back in.":
      "Are you sure you want to log out? You will need to enter your credentials to log back in.",
  "Are you sure you want to delete your account? This action is irreversible and will permanently remove all your data.":
      "Are you sure you want to delete your account? This action is irreversible and will permanently remove all your data.",
  'Delete': 'Delete',
  'Delete Account': 'Delete Account',
  "Account deleted successfully": "Account deleted successfully",
  "Contact Administrator": "Contact Administrator",
  'Email': 'Email',
  'Email Address': 'Email Address',
  'Enter email address': 'Enter email address',
  'Password': 'Password',
  'Enter password': 'Enter password',
  'Forgot Password': 'Forgot Password',
  'Deliver to the': 'Deliver to the',
  'Variants': 'Variants',
  'Addons': 'Addons',
  'Continue with Mobile Number': 'Continue with Mobile Number',
  'with Google': 'with Google',
  'with Apple': 'with Apple',
  'Please enter valid email': 'Please enter valid email',
  'Please enter valid password': 'Please enter valid password',
  'Log in': 'Log in',
  'Verify Your Mobile Number': 'Verify Your Mobile Number',
  'Enter the OTP sent to your mobile number to verify and secure your account.':
      'Enter the OTP sent to your mobile number to verify and secure your account.',
  'Did’t receive any code? ': 'Did’t receive any code? ',
  'Send Again': 'Send Again',
  'Already Have an account?': 'Already Have an account?',
  'Verify otp': 'Verify otp',
  'This user is disable please contact to administrator':
      'This user is disable please contact to administrator',
  'Account already created in other application. You are not able login this application.':
      'Account already created in other application. You are not able login this application.',
  'Invalid Code': 'Invalid Code',
  'Enter Valid otp': 'Enter Valid otp',
  'Send Code': 'Send Code',
  'Log In Using Your Mobile Number': 'Log In Using Your Mobile Number',
  'Enter your mobile number to quickly access your account and start managing your deliveries.':
      'Enter your mobile number to quickly access your account and start managing your deliveries.',
  'Phone Number': 'Phone Number',
  'Enter Phone Number': 'Enter Phone Number',
  'Log in with': 'Log in with',
  'E-mail': 'E-mail',
  'Please enter mobile number': 'Please enter mobile number',
  'Create an Account': 'Create an Account',
  'Sign up now to start your journey as a Foodie driver and begin earning with every delivery.':
      'Sign up now to start your journey as a ${AppBranding.baseBrandName} driver and begin earning with every delivery.',
  'First Name': 'First Name',
  'Enter First Name': 'Enter First Name',
  'Last Name': 'Last Name',
  'Enter Last Name': 'Enter Last Name',
  'Enter Email Address': 'Enter Email Address',
  'Zone': 'Zone',
  'Select zone': 'Select zone',
  'Confirm Password': 'Confirm Password',
  'Save': 'Save',
  'Please enter stripe account Id': 'Please enter stripe account Id',
  'Enter Confirm Password': 'Enter Confirm Password',
  'Please enter first name': 'Please enter first name',
  'Please enter last name': 'Please enter last name',
  'Please enter Phone number': 'Please enter Phone number',
  'Please select zone': 'Please select zone',
  'Please enter password': 'Please enter password',
  'Please enter Confirm password': 'Please enter Confirm password',
  "Password and Confirm password doesn't match":
      "Password and Confirm password doesn't match",
  "customerName": "customerName",
  "storeName": "storeName",
  "orderId": "orderId",
  "storeId": "storeId",
  "customerId": "customerId",
  "customerProfileImage": "customerProfileImage",
  "storeProfileImage": "storeProfileImage",
  "token": "token",
  "chatType": "chatType",
  "V": "V",
  "Edit Profile": "Edit Profile",
  "please select": "please select",
  "No worries!! We’ll send you reset instructions":
      "No worries!! We’ll send you reset instructions",
  'Give': 'Give',
  'Items to the customer': 'Items to the customer',
  "Conform Deliver order": "Conform Deliver order",
  'Order Delivered': 'Order Delivered',
  'Order': 'Order',
  "Make Order Delivered": "Make Order Delivered",
  "Document Verification in Pending": "Document Verification in Pending",
  "Your documents are being reviewed. We will notify you once the verification is complete.":
      "Your documents are being reviewed. We will notify you once the verification is complete.",
  "View Status": "View Status",
  'You have to minimum': 'You have to minimum',
  'wallet amount to receiving Order': 'wallet amount to receiving Order',
  'Navigate with': 'Navigate with',
  "New": "New",
  "Active": "Active",
  'New Order not found.': 'New Order not found.',
  "Trip Distance": "Trip Distance",
  "Delivery Charge": "Delivery Charge",
  'for Verification': 'for Verification',
  'Upload': 'Upload',
  'Order Not found': 'Order Not found',
  "Tips": "Tips",
  "Reject": "Reject",
  "Accept": "Accept",
  "Active order not found.": "Active order not found.",
  "Vandex Map": "Vandex Map",
  "Navigate with ": "Navigate with ",
  "google": "google",
  "Google Map": "Google Map",
  "googleGo": "googleGo",
  "waze": "waze",
  "Waze Map": "Waze Map",
  "mapswithme": "mapswithme",
  "MapsWithMe Map": "MapsWithMe Map",
  "yandexNavi": "yandexNavi",
  "VandexNavi Map": "VandexNavi Map",
  'Easily find your destination with a single tap redirect to':
      'Easily find your destination with a single tap redirect to',
  'Redirect': 'Redirect',
  'for seamless navigation.': 'for seamless navigation.',
  "Payment Type": "Payment Type",
  "Collect Payment from customer": "Collect Payment from customer",
  "Reached store for Pickup": "Reached store for Pickup",
  "Reached the Customers Door Steps": "Reached the Customers Door Steps",
  "Order Ready to pickup": "Order Ready to pickup",
  "Your order has been ready pickup the order and deliver to the customer’s locations.":
      "Your order has been ready pickup the order and deliver to the customer’s locations.",
  "Item and Deliver to the": "Item and Deliver to the",
  "x": "x",
  "Confirm Pickup": "Confirm Pickup",
  "Conform pickup order": "Conform pickup order",
  "Picked Order": "Picked Order",
  "Order Details": "Order Details",
  "Bill Details": "Bill Details",
  "Item totals": "Item totals",
  "Delivery Fee": "Delivery Fee",
  "Coupon Discount": "Coupon Discount",
  "Special Discount": "Special Discount",
  "Delivery Tips": "Delivery Tips",
  "To Pay": "To Pay",
  "Order ID": "Order ID",
  "Status": "Status",
  "Date": "Date",
  'Please upload a valid': 'Please upload a valid',
  "Upload for Verification": "Upload for Verification",
  'to verify your identity complete the registration process.':
      'to verify your identity complete the registration process.',
  "Front Side of": "Front Side of",
  "Choose a image and upload here": "Choose a image and upload here",
  "JPEG, PNG": "JPEG, PNG",
  "Brows Image": "Brows Image",
  "Back side of": "Back side of",
  "approved": "approved",
  "uploaded": "uploaded",
  "Please upload front side of document.":
      "Please upload front side of document.",
  "Please upload back side of document.":
      "Please upload back side of document.",
  'Please wait..': 'Please wait..',
  'Payment Method remove successfully': 'Payment Method remove successfully',
  "Upload Document": "Upload Document",
  "Please Select": "Please Select",
  'camera': 'camera',
  "Gallery": "Gallery",
  'Photo': 'Photo',
  "Upload your ID Proof to complete the verification process and ensure compliance.":
      "Upload your ID Proof to complete the verification process and ensure compliance.",
  "Front": "Front",
  "And Back": "And Back",
  "Verified": "Verified",
  "Top up Wallet": "Top up Wallet",
  "Top-up": "Top-up",
  "Please Enter minimum amount of": "Please Enter minimum amount of",
  'Something went wrong, please contact admin.':
      'Something went wrong, please contact admin.',
  'Please select payment method': 'Please select payment method',
  "My Wallet": "My Wallet",
  "Withdraw": "Withdraw",
  "Please enter payment method": "Please enter payment method",
  "Top up History": "Top up History",
  'Transaction History': 'Transaction History',
  "Transaction history not found": "Transaction history not found",
  'Withdrawal History': 'Withdrawal History',
  'Withdrawal': 'Withdrawal',
  "Withdrawal history not found": "Withdrawal history not found",
  'Amount': 'Amount',
  'Add Notes': 'Add Notes',
  'Select Withdraw Method': 'Select Withdraw Method',
  'Enter Amount': 'Enter Amount',
  "Withdrawal amount": "Withdrawal amount",
  "Enter withdrawal amount": "Enter withdrawal amount",
  "Notes": "Notes",
  "Bank Transfer": "Bank Transfer",
  "Flutter wave": "Flutter wave",
  "PayPal": "PayPal",
  "RazorPay": "RazorPay",
  "Stripe": "Stripe",
  'Please enter amount': 'Please enter amount',
  'Withdraw amount must be greater or equal to':
      'Withdraw amount must be greater or equal to',
  "Pending": "Pending",
  "bank": "bank",
  "Success": "Success",
  "Completed Delivery": "Completed Delivery",
  "Bank Setup": "Bank Setup",
  'Bank Name': 'Bank Name',
  'Enter Bank Name': 'Enter Bank Name',
  'Enter Branch Name': 'Enter Branch Name',
  'Branch Name': 'Branch Name',
  "Holder Name": "Holder Name",
  'Account Number': 'Account Number',
  'Bank Code': 'Bank Code',
  'Enter Account Number': 'Enter Account Number',
  'Other Information': 'Other Information',
  'Enter Other Information': 'Enter Other Information',
  'Please enter bank name': 'Please enter bank name',
  'Please enter branch name': 'Please enter branch name',
  'Please enter holder name': 'Please enter holder name',
  'Please enter account number': 'Please enter account number',
  "Enter Holder Name": "Enter Holder Name",
  "Your Setup is pending": "Your Setup is pending",
  "Setup now": "Setup now",
  'Save Details': 'Save Details',
  "Setup was done.": "Setup was done.",
  'Skip': 'Skip',
  'Welcome to GroMart Driver': AppBranding.driverWelcomeMessage,
  'You have to allow location permission to use your location':
      'You have to allow location permission to use your location',
  'Get Started': 'Get Started',
  'Your Favorite Items Delivered Fast!': 'Your Favorite Items Delivered Fast!',
  'Next': 'Next',
  'Welcome to Foodie Driver': AppBranding.driverWelcomeMessage,
  'Your Favorite Food Delivered Fast!': 'Your Favorite Food Delivered Fast!',
  'Payment Method save successfully': 'Payment Method save successfully',
  "Please enter account Number": "Please enter account Number",
  "Please enter bank code": "Please enter bank code",
  "Paypal Email": "Paypal Email",
  "Please enter Paypal email": "Please enter Paypal email",
  "Razorpay account Id": "Razorpay account Id",
  "Add your Account ID. For example, acc_GLGeLkU2JUeyDZ":
      "Add your Account ID. For example, acc_GLGeLkU2JUeyDZ",
  "Please enter RazorPay account Id": "Please enter RazorPay account Id",
  "Stripe Account Id": "Stripe Account Id",
  'Go to your Stripe account settings > Account details > Copy your account ID on the right-hand side. For example, acc_GLGeLkU2JUeyDZ':
      'Go to your Stripe account settings > Account details > Copy your account ID on the right-hand side. For example, acc_GLGeLkU2JUeyDZ',
  "This user is not created in driver application.":
      "This user is not created in driver application.",
  'sent a message': 'sent a message',
  'Sent a video': 'Sent a video',
  'Sent a audio': 'Sent a audio',
  'No user found for that email.': 'No user found for that email.',
  'email': 'email',
  'Reset Password link sent your': 'Reset Password link sent your',
  '"No user found for that email.': '"No user found for that email.',
  'Wrong password provided for that user.':
      'Wrong password provided for that user.',
  'Invalid Email.': 'Invalid Email.',
  'please wait...': 'please wait...',
  'OTP sent': 'OTP sent',
  'Account create successfully': 'Account create successfully',
  'Thank you for sign up, your application is under approval so please wait till that approve.':
      'Thank you for sign up, your application is under approval so please wait till that approve.',
  'The password provided is too weak.': 'The password provided is too weak.',
  'Enter email is Invalid': 'Enter email is Invalid',
  'The account already exists for that email.':
      'The account already exists for that email.',
  'Document upload successfully': 'Document upload successfully',
  'Amount Top-up successfully': 'Amount Top-up successfully',
  'Payment Successful!!': 'Payment Successful!!',
  'Payment UnSuccessful!!': 'Payment UnSuccessful!!',
  'Payment successfully': 'Payment successfully',
  'Payment Failed': 'Payment Failed',
  'something went wrong, please contact admin.':
      'something went wrong, please contact admin.',
  'Payment Processing!! via': 'Payment Processing!! via',
  'Payment Failed!!': 'Payment Failed!!',
  'Cancel Payment': 'Cancel Payment',
  'cancelPayment?': 'cancelPayment?',
  'Continue': 'Continue',
  'Exit': 'Exit',
  'Continue Payment': 'Continue Payment',
  'Location Picker': 'Location Picker',
  'Search Address': 'Search Address'
};
